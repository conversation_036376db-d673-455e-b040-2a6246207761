"use client";

import Image from "next/image";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  CardHeader,
  Input,
  Navbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  Link,
  Chip,
  Avatar,
  Divider,
  Spacer,
} from "@heroui/react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Navigation */}
      <Navbar isBordered>
        <NavbarBrand>
          <Image
            src="/next.svg"
            alt="Next.js logo"
            width={120}
            height={25}
            className="dark:invert"
          />
          <p className="font-bold text-inherit ml-2">ZP Market</p>
        </NavbarBrand>
        <NavbarContent className="hidden sm:flex gap-4" justify="center">
          <NavbarItem>
            <Link color="foreground" href="#">
              Features
            </Link>
          </NavbarItem>
          <NavbarItem isActive>
            <Link href="#" aria-current="page">
              Home
            </Link>
          </NavbarItem>
          <NavbarItem>
            <Link color="foreground" href="#">
              About
            </Link>
          </NavbarItem>
        </NavbarContent>
        <NavbarContent justify="end">
          <NavbarItem className="hidden lg:flex">
            <Link href="#">Login</Link>
          </NavbarItem>
          <NavbarItem>
            <Button as={Link} color="primary" href="#" variant="flat">
              Sign Up
            </Button>
          </NavbarItem>
        </NavbarContent>
      </Navbar>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Welcome to ZP Market
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Experience the power of HeroUI components in a modern Next.js application.
            Beautiful, accessible, and highly customizable.
          </p>
          <div className="flex gap-4 justify-center flex-wrap">
            <Button color="primary" size="lg" className="font-semibold">
              Get Started
            </Button>
            <Button variant="bordered" size="lg" className="font-semibold">
              Learn More
            </Button>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <Card className="py-4">
            <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
              <Chip color="primary" variant="flat" className="mb-2">
                Modern
              </Chip>
              <h4 className="font-bold text-large">Beautiful Design</h4>
            </CardHeader>
            <CardBody className="overflow-visible py-2">
              <p className="text-default-500">
                Clean, modern interface built with HeroUI components that adapt to your brand.
              </p>
            </CardBody>
          </Card>

          <Card className="py-4">
            <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
              <Chip color="success" variant="flat" className="mb-2">
                Accessible
              </Chip>
              <h4 className="font-bold text-large">Fully Accessible</h4>
            </CardHeader>
            <CardBody className="overflow-visible py-2">
              <p className="text-default-500">
                Built with accessibility in mind, ensuring everyone can use your application.
              </p>
            </CardBody>
          </Card>

          <Card className="py-4">
            <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
              <Chip color="secondary" variant="flat" className="mb-2">
                Responsive
              </Chip>
              <h4 className="font-bold text-large">Mobile First</h4>
            </CardHeader>
            <CardBody className="overflow-visible py-2">
              <p className="text-default-500">
                Responsive design that works perfectly on all devices and screen sizes.
              </p>
            </CardBody>
          </Card>
        </div>

        {/* Interactive Demo Section */}
        <Card className="max-w-4xl mx-auto mb-12">
          <CardHeader>
            <h3 className="text-2xl font-bold">Interactive Demo</h3>
          </CardHeader>
          <CardBody className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Input
                type="email"
                label="Email"
                placeholder="Enter your email"
                className="flex-1"
              />
              <Input
                type="text"
                label="Name"
                placeholder="Enter your name"
                className="flex-1"
              />
            </div>

            <div className="flex gap-2 flex-wrap">
              <Button color="primary" variant="solid">Primary</Button>
              <Button color="secondary" variant="flat">Secondary</Button>
              <Button color="success" variant="bordered">Success</Button>
              <Button color="warning" variant="light">Warning</Button>
              <Button color="danger" variant="ghost">Danger</Button>
            </div>

            <Divider />

            <div className="flex items-center gap-4">
              <Avatar
                src="https://i.pravatar.cc/150?u=a042581f4e29026024d"
                className="w-12 h-12"
              />
              <div>
                <p className="font-semibold">John Doe</p>
                <p className="text-small text-default-500">Product Designer</p>
              </div>
              <Spacer />
              <Chip color="success" variant="dot">
                Online
              </Chip>
            </div>
          </CardBody>
        </Card>
      </main>

      {/* Footer */}
      <footer className="border-t border-divider bg-content1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              <Link
                isExternal
                href="https://nextjs.org/learn"
                className="flex items-center gap-2 text-default-500 hover:text-default-700"
              >
                <Image
                  src="/file.svg"
                  alt="File icon"
                  width={16}
                  height={16}
                />
                Learn
              </Link>
              <Link
                isExternal
                href="https://vercel.com/templates"
                className="flex items-center gap-2 text-default-500 hover:text-default-700"
              >
                <Image
                  src="/window.svg"
                  alt="Window icon"
                  width={16}
                  height={16}
                />
                Examples
              </Link>
              <Link
                isExternal
                href="https://nextjs.org"
                className="flex items-center gap-2 text-default-500 hover:text-default-700"
              >
                <Image
                  src="/globe.svg"
                  alt="Globe icon"
                  width={16}
                  height={16}
                />
                Next.js
              </Link>
            </div>
            <p className="text-small text-default-500">
              Built with HeroUI and Next.js
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
